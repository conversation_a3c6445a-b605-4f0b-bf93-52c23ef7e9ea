/* Dashboard CSS - ChatGPT Style */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: #212121;
    color: #ffffff;
    overflow: hidden;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: #171717;
    border-right: 1px solid #2f2f2f;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1000;
}

.sidebar.collapsed {
    width: 60px;
}

/* Sidebar Header */
.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #2f2f2f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
}

.logo i {
    font-size: 1.5rem;
    color: #10a37f;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: #2f2f2f;
    color: #ffffff;
}

/* New Chat Button */
.new-chat-section {
    padding: 16px;
}

.new-chat-btn {
    width: 100%;
    background: none;
    border: 1px solid #2f2f2f;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: inherit;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: #2f2f2f;
}

.new-chat-btn i {
    font-size: 1rem;
}

/* Search Section */
.search-section {
    padding: 0 16px 16px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: #8e8ea0;
    font-size: 0.9rem;
}

.search-box input {
    width: 100%;
    background: #2f2f2f;
    border: none;
    color: #ffffff;
    padding: 10px 12px 10px 35px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.9rem;
}

.search-box input:focus {
    outline: none;
    background: #3f3f3f;
}

.search-box input::placeholder {
    color: #8e8ea0;
}

/* Navigation Menu */
.nav-menu {
    padding: 0 16px;
    border-bottom: 1px solid #2f2f2f;
    margin-bottom: 16px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    color: #8e8ea0;
    font-size: 0.9rem;
    margin-bottom: 4px;
    transition: all 0.2s ease;
}

.nav-item:hover {
    background: #2f2f2f;
    color: #ffffff;
}

.nav-item.active {
    background: #2f2f2f;
    color: #ffffff;
}

.nav-item i {
    font-size: 1rem;
    width: 16px;
}

/* Chat History */
.chat-history-section {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
}

.history-header {
    margin-bottom: 8px;
    margin-top: 16px;
}

.history-header:first-child {
    margin-top: 0;
}

.history-header h3 {
    color: #8e8ea0;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chat-list {
    margin-bottom: 16px;
}

.chat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    color: #8e8ea0;
    font-size: 0.9rem;
    margin-bottom: 2px;
    transition: all 0.2s ease;
    position: relative;
}

.chat-item:hover {
    background: #2f2f2f;
    color: #ffffff;
}

.chat-item.active {
    background: #10a37f;
    color: #ffffff;
}

.chat-item i {
    font-size: 0.8rem;
    width: 12px;
}

.chat-item-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-item-menu {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chat-item:hover .chat-item-menu {
    opacity: 1;
}

/* User Profile Section */
.user-profile-section {
    padding: 16px;
    border-top: 1px solid #2f2f2f;
    position: relative;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-profile:hover {
    background: #2f2f2f;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #10a37f;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.user-avatar i {
    color: #ffffff;
    font-size: 0.9rem;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-status {
    color: #8e8ea0;
    font-size: 0.8rem;
}

.profile-dropdown-btn {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.profile-dropdown-btn:hover {
    background: #3f3f3f;
    color: #ffffff;
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    bottom: 100%;
    left: 16px;
    right: 16px;
    background: #2f2f2f;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    padding: 8px 0;
    display: none;
    z-index: 1001;
}

.user-dropdown.show {
    display: block;
    animation: slideUp 0.2s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 16px;
    color: #ffffff;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #3f3f3f;
}

.dropdown-item i {
    width: 16px;
    font-size: 0.9rem;
    color: #8e8ea0;
}

.dropdown-item:hover i {
    color: #ffffff;
}

.dropdown-item:last-child {
    margin-left: auto;
}

.dropdown-divider {
    height: 1px;
    background: #3f3f3f;
    margin: 8px 0;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #212121;
    position: relative;
}

/* API Status Banner */
.api-status-banner {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 12px 24px;
    border-bottom: 1px solid #92400e;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: 12px;
}

.banner-content i {
    font-size: 1.1rem;
    margin-right: 8px;
}

.banner-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.banner-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Header */
.main-header {
    padding: 16px 24px;
    border-bottom: 1px solid #2f2f2f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.mobile-menu-btn:hover {
    background: #2f2f2f;
    color: #ffffff;
}

.header-left h1 {
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 600;
}

.upgrade-btn {
    background: linear-gradient(135deg, #10a37f, #0d8f6f);
    border: none;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.upgrade-btn:hover {
    background: linear-gradient(135deg, #0d8f6f, #0a7a5e);
    transform: translateY(-1px);
}

/* Welcome Screen */
.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
}

.welcome-content {
    max-width: 768px;
    width: 100%;
    text-align: center;
}

#welcomeTitle {
    color: #ffffff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 32px;
}

/* Chat Input Container */
.chat-input-container {
    position: relative;
    max-width: 100%;
}

.chat-input-wrapper {
    background: #2f2f2f;
    border: 1px solid #3f3f3f;
    border-radius: 24px;
    padding: 12px 16px;
    display: flex;
    align-items: flex-end;
    gap: 8px;
    transition: all 0.2s ease;
}

.chat-input-wrapper:focus-within {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.chat-input-wrapper textarea {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
}

.chat-input-wrapper textarea::placeholder {
    color: #8e8ea0;
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: #3f3f3f;
    color: #ffffff;
}

.action-btn:last-child {
    background: #10a37f;
    color: #ffffff;
}

.action-btn:last-child:hover {
    background: #0d8f6f;
}

.tools-btn {
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid #3f3f3f;
}

/* Chat Interface */
.chat-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 24px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: #3f3f3f;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: #2f2f2f;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #3f3f3f;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    color: #ffffff;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: #8e8ea0;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #3f3f3f;
    color: #ffffff;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(33, 33, 33, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    text-align: center;
    color: #ffffff;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #10a37f;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 4000;
}

.toast {
    background: #2f2f2f;
    border: 1px solid #3f3f3f;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    color: #ffffff;
    min-width: 300px;
    animation: slideInRight 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast.success {
    border-left: 4px solid #10a37f;
}

.toast.error {
    border-left: 4px solid #ef4444;
}

.toast.warning {
    border-left: 4px solid #f59e0b;
}

.toast.info {
    border-left: 4px solid #3b82f6;
}

/* Message Styles */
.message {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    animation: messageSlideIn 0.3s ease;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
}

.message.user .message-avatar {
    background: #10a37f;
}

.message.ai .message-avatar {
    background: #ef4444;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.message-avatar i {
    color: #ffffff;
    font-size: 1rem;
}

.message-content {
    background: #2f2f2f;
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 70%;
    border: 1px solid #3f3f3f;
}

.message.user .message-content {
    background: #10a37f;
    border-color: #0d8f6f;
}

.message-text {
    color: #ffffff;
    line-height: 1.6;
    word-wrap: break-word;
}

.message-time {
    font-size: 0.8rem;
    color: #8e8ea0;
    margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -260px;
        height: 100vh;
        z-index: 1001;
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        width: 100%;
    }

    .mobile-menu-btn {
        display: block;
    }

    .welcome-content {
        padding: 16px;
    }

    #welcomeTitle {
        font-size: 1.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .toast {
        min-width: 280px;
        margin-right: 10px;
    }

    .message-content {
        max-width: 85%;
    }
}
