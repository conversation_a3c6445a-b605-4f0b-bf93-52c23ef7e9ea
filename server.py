#!/usr/bin/env python3
"""
Tincada AI - Simple HTTP Server
Server fudud oo loogu talagalay in lagu test gareeyyo website-ka Tincada AI
"""

import http.server
import socketserver
import os
import webbrowser
from urllib.parse import urlparse, parse_qs
import json
import threading
import time

class TincadaAIHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()
    
    def log_message(self, format, *args):
        # Custom log format
        print(f"[Tincada AI Server] {format % args}")

def start_server(port=5000):
    """Start the Tincada AI server"""
    try:
        with socketserver.TCPServer(("", port), TincadaAIHandler) as httpd:
            print(f"""
╔══════════════════════════════════════════════════════════════╗
║                        TINCADA AI SERVER                     ║
╠══════════════════════════════════════════════════════════════╣
║  🤖 Server waa la bilaabay: http://localhost:{port}           ║
║  📁 Directory: {os.getcwd():<40} ║
║  🌐 Browser waa la furi doonaa...                           ║
║  ⏹️  Server joojinta: Ctrl+C                                ║
╚══════════════════════════════════════════════════════════════╝
            """)
            
            # Open browser after a short delay
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{port}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n[Tincada AI Server] Server waa la joojiyay. Mahadsanid!")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {port} waa la isticmaalayaa. Isku day port kale:")
            print(f"   python server.py --port {port + 1}")
        else:
            print(f"❌ Khalad: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Tincada AI Server')
    parser.add_argument('--port', '-p', type=int, default=5000, 
                       help='Port number (default: 5000)')
    
    args = parser.parse_args()
    
    # Check if required files exist
    required_files = ['index.html', 'styles.css', 'script.js']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ Files la waayay:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nFadlan hubi in dhammaan files-yada ay jiraan directory-ga hadda.")
        return
    
    print("✅ Dhammaan files-yada waa la helay!")
    start_server(args.port)

if __name__ == '__main__':
    main()
