// Tincada AI - Dashboard JavaScript
class TincadaDashboard {
    constructor() {
        // Primary API key (OpenAI format)
        this.apiKey = '********************************************************************************************************************************************************************';
        this.apiUrl = 'https://api.openai.com/v1/chat/completions';
        // Fallback API key (alternative format)
        this.fallbackApiKey = 'sk-1d9fea924a444e58b28769de9662ef20';
        this.currentUser = null;
        this.currentChatId = null;
        this.chatHistory = [];
        this.chats = [];
        this.messageCount = 0;
        this.maxMessages = 1000; // Daily limit
        this.currentChatMessageCount = 0; // Messages in current chat
        this.maxMessagesPerChat = 100; // Auto new chat after 100 messages
        this.dailyMessageCount = 0; // Total daily messages
        this.useMockAPI = false; // Set to true to use mock responses instead of real API
        
        this.init();
    }

    init() {
        this.checkAuthentication();
        this.loadSavedSettings();
        this.bindEvents();
        this.loadUserProfile();
        this.loadChatHistory();
        this.setupAutoResize();
        this.updateAPIStatusBanner();
    }

    loadSavedSettings() {
        // Load saved API key and mock mode setting
        const savedApiKey = localStorage.getItem('tincadaAI_apiKey');
        const savedMockMode = localStorage.getItem('tincadaAI_useMockAPI');

        if (savedApiKey) {
            this.apiKey = savedApiKey;
        }

        if (savedMockMode) {
            this.useMockAPI = savedMockMode === 'true';
        }

        // Load daily message count
        this.loadDailyMessageCount();

        console.log('🔧 Settings loaded:', {
            apiKey: this.apiKey.substring(0, 20) + '...',
            useMockAPI: this.useMockAPI,
            dailyMessages: this.dailyMessageCount
        });
    }

    loadDailyMessageCount() {
        const today = new Date().toDateString();
        const savedDate = localStorage.getItem('tincadaAI_messageDate');
        const savedCount = localStorage.getItem('tincadaAI_dailyMessageCount');

        if (savedDate === today && savedCount) {
            this.dailyMessageCount = parseInt(savedCount);
        } else {
            // New day, reset count
            this.dailyMessageCount = 0;
            localStorage.setItem('tincadaAI_messageDate', today);
            localStorage.setItem('tincadaAI_dailyMessageCount', '0');
        }

        this.updateMessageCountDisplay();
    }

    saveDailyMessageCount() {
        const today = new Date().toDateString();
        localStorage.setItem('tincadaAI_messageDate', today);
        localStorage.setItem('tincadaAI_dailyMessageCount', this.dailyMessageCount.toString());
        this.updateMessageCountDisplay();
    }

    updateMessageCountDisplay() {
        // Update message count display in UI
        const remaining = this.maxMessages - this.dailyMessageCount;
        const counterElement = document.getElementById('messageCounter');
        const countText = document.getElementById('messageCountText');

        if (countText) {
            countText.textContent = `${this.dailyMessageCount}/${this.maxMessages}`;

            // Update counter styling based on usage
            if (this.dailyMessageCount > this.maxMessages * 0.9) {
                counterElement.className = 'message-counter danger';
            } else if (this.dailyMessageCount > this.maxMessages * 0.7) {
                counterElement.className = 'message-counter warning';
            } else {
                counterElement.className = 'message-counter';
            }
        }

        console.log(`📊 Messages: ${this.dailyMessageCount}/${this.maxMessages} (${remaining} remaining)`);
    }

    checkAuthentication() {
        if (!authManager.isAuthenticated() || !authManager.isSessionValid()) {
            window.location.href = 'login.html';
            return;
        }
        
        this.currentUser = authManager.getCurrentUser();
        authManager.refreshSession();
    }

    loadUserProfile() {
        if (!this.currentUser) return;
        
        // Update user name
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = this.currentUser.fullName;
        }
        
        // Update user avatar
        const userAvatarElement = document.getElementById('userAvatar');
        if (userAvatarElement && this.currentUser.profileImage) {
            userAvatarElement.innerHTML = `<img src="${this.currentUser.profileImage}" alt="${this.currentUser.fullName}">`;
        }
        
        // Update welcome title
        const welcomeTitle = document.getElementById('welcomeTitle');
        if (welcomeTitle) {
            const firstName = this.currentUser.fullName.split(' ')[0];
            welcomeTitle.textContent = `Maxaad ka fikirtaa maanta, ${firstName}?`;
        }
    }

    bindEvents() {
        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // Mobile menu
        document.getElementById('mobileMenuBtn').addEventListener('click', () => {
            this.toggleMobileSidebar();
        });
        
        // New chat button
        document.getElementById('newChatBtn').addEventListener('click', () => {
            this.startNewChat();
        });
        
        // User profile dropdown
        document.getElementById('profileDropdownBtn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleUserDropdown();
        });
        
        // Dropdown menu items
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.openSettings();
        });
        
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });
        
        // Chat input handling
        document.getElementById('mainChatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage('main');
            }
        });
        
        document.getElementById('chatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage('chat');
            }
        });
        
        // Send buttons
        document.getElementById('sendBtn').addEventListener('click', () => {
            this.sendMessage('main');
        });
        
        document.getElementById('chatSendBtn').addEventListener('click', () => {
            this.sendMessage('chat');
        });
        
        // File upload
        document.getElementById('attachBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        document.getElementById('chatAttachBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        // Voice buttons
        document.getElementById('voiceBtn').addEventListener('click', () => {
            this.toggleVoiceRecording();
        });
        
        document.getElementById('chatVoiceBtn').addEventListener('click', () => {
            this.toggleVoiceRecording();
        });
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchChats(e.target.value);
        });
        
        // Navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                this.switchNavigation(item.id);
            });
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            this.closeUserDropdown();
        });
        
        // Close modal when clicking outside
        document.getElementById('settingsModal').addEventListener('click', (e) => {
            if (e.target.id === 'settingsModal') {
                this.closeSettings();
            }
        });

        document.getElementById('closeSettingsModal').addEventListener('click', () => {
            this.closeSettings();
        });

        // Close API status banner
        document.getElementById('closeBannerBtn').addEventListener('click', () => {
            this.hideAPIStatusBanner();
        });

        // Payment modal events
        document.getElementById('closePaymentModal').addEventListener('click', () => {
            this.closePaymentModal();
        });

        document.getElementById('paymentModal').addEventListener('click', (e) => {
            if (e.target.id === 'paymentModal') {
                this.closePaymentModal();
            }
        });

        // Plan selection buttons
        document.querySelectorAll('.plan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const plan = e.target.closest('.plan-btn').dataset.plan;
                this.selectPlan(plan);
            });
        });

        // Get Plus button
        document.querySelector('.upgrade-btn').addEventListener('click', () => {
            this.showUpgradeModal();
        });

        // Payment form modal events
        document.getElementById('closePaymentFormModal').addEventListener('click', () => {
            this.closePaymentFormModal();
        });

        document.getElementById('paymentFormModal').addEventListener('click', (e) => {
            if (e.target.id === 'paymentFormModal') {
                this.closePaymentFormModal();
            }
        });

        document.getElementById('backToPlanBtn').addEventListener('click', () => {
            this.backToPlans();
        });

        document.getElementById('paymentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processPayment();
        });
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    toggleUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');
    }

    closeUserDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.remove('show');
    }

    startNewChat() {
        this.currentChatId = this.generateChatId();
        this.chatHistory = [];
        this.currentChatMessageCount = 0; // Reset current chat message count

        // Switch to chat interface
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('chatInterface').style.display = 'flex';

        // Clear chat messages
        document.getElementById('chatMessages').innerHTML = '';

        // Add to chat list
        this.addChatToList(this.currentChatId, 'Sheeko Cusub');

        // Focus on chat input
        document.getElementById('chatInput').focus();

        console.log('🔄 New chat started:', {
            chatId: this.currentChatId,
            dailyMessages: this.dailyMessageCount,
            currentChatMessages: this.currentChatMessageCount
        });
    }

    async sendMessage(inputType) {
        const inputId = inputType === 'main' ? 'mainChatInput' : 'chatInput';
        const messageInput = document.getElementById(inputId);
        const message = messageInput.value.trim();

        if (!message) return;

        // Check daily message limit
        if (this.dailyMessageCount >= this.maxMessages) {
            this.showPaymentModal();
            return;
        }

        // Check if current chat needs to be reset (100 messages)
        if (this.currentChatMessageCount >= this.maxMessagesPerChat) {
            this.showToast(`Chat waa la dhamaystay (${this.maxMessagesPerChat} fariin). Sheeko cusub ayaa la bilaabayaa...`, 'info');
            this.startNewChat();
            // Update input reference after new chat
            const newInputId = inputType === 'main' ? 'mainChatInput' : 'chatInput';
            const newMessageInput = document.getElementById(newInputId);
            if (newMessageInput) {
                newMessageInput.value = message; // Restore the message
            }
        }

        // If sending from main input, start new chat
        if (inputType === 'main') {
            this.startNewChat();
        }

        // Add user message
        this.addMessage('user', message);
        messageInput.value = '';
        this.autoResizeTextarea(messageInput);
        
        // Show loading
        this.showLoading(true);
        
        try {
            const messages = [
                {
                    role: 'system',
                    content: 'Waxaad tahay Tincada AI, kaaliye AI ah oo af Soomaali ku hadla. Waxaad bixisaa jawaabo faa\'iido leh, qurux badan, oo af Soomaali nadiif ah. Marwalba ku jawaab af Soomaali, haddii aan si gaar ah loogu sheegin luqad kale.'
                },
                ...this.chatHistory.slice(-10).map(msg => ({
                    role: msg.type === 'user' ? 'user' : 'assistant',
                    content: msg.text
                })),
                {
                    role: 'user',
                    content: message
                }
            ];

            let response = await this.makeAPIRequest(messages, this.apiKey);

            // If first API key fails, try fallback
            if (!response.success && this.fallbackApiKey) {
                console.log('Primary API key failed, trying fallback...');
                response = await this.makeAPIRequest(messages, this.fallbackApiKey);
            }

            if (!response.success) {
                throw new Error(response.error || 'API request failed');
            }

            const aiResponse = response.data.choices[0].message.content;

            // Add AI response
            this.addMessage('ai', aiResponse);

            // Update chat title if it's the first message
            if (this.chatHistory.length === 2) {
                this.updateChatTitle(this.currentChatId, message.substring(0, 30) + '...');
            }

        } catch (error) {
            console.error('Error:', error);

            // More specific error messages
            let errorMessage = 'Waan ka xumahay, khalad ayaa dhacay. Fadlan mar kale isku day.';
            let toastMessage = 'Khalad ayaa dhacay API-ga';

            if (error.message.includes('401')) {
                errorMessage = 'API key-gu ma sax aha. Fadlan hubi API key-ga.';
                toastMessage = 'API key khalad ah';
            } else if (error.message.includes('429')) {
                errorMessage = 'Aad bay u badan tahay codsashada. Fadlan dhawr daqiiqo sug.';
                toastMessage = 'Rate limit - sug dhawr daqiiqo';
            } else if (error.message.includes('Network')) {
                errorMessage = 'Internet connection khalad ah. Hubi connection-kaaga.';
                toastMessage = 'Internet connection khalad';
            } else if (error.message.includes('500')) {
                errorMessage = 'Server-ka OpenAI wuu khalad qabaa. Fadlan mar kale isku day.';
                toastMessage = 'OpenAI server khalad';
            }

            this.addMessage('ai', errorMessage);
            this.showToast(toastMessage, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async makeAPIRequest(messages, apiKey) {
        console.log('🔄 Making API request with key:', apiKey.substring(0, 20) + '...');
        console.log('📝 Messages:', messages);

        // Mock API Response for demo purposes (remove when you have working API key)
        if (this.useMockAPI) {
            return this.getMockResponse(messages);
        }

        try {
            const requestBody = {
                model: 'gpt-4o-mini',
                messages: messages,
                max_tokens: 1000,
                temperature: 0.7
            };

            console.log('📤 Request body:', requestBody);

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify(requestBody)
            });

            console.log('📥 Response status:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ API Error ${response.status}:`, errorText);

                // If API fails, switch to mock mode
                console.log('🔄 Switching to mock API mode...');
                this.useMockAPI = true;
                this.showToast('API key ma shaqaynayo. Mock responses la isticmaalayaa.', 'warning');
                return this.getMockResponse(messages);
            }

            const data = await response.json();
            console.log('✅ API Response:', data);
            return { success: true, data: data };

        } catch (error) {
            console.error('🌐 Network error:', error);

            // If network fails, switch to mock mode
            console.log('🔄 Switching to mock API mode due to network error...');
            this.useMockAPI = true;
            this.showToast('Network khalad. Mock responses la isticmaalayaa.', 'warning');
            return this.getMockResponse(messages);
        }
    }

    getMockResponse(messages) {
        console.log('🤖 Using mock API response');

        // Get the user's message
        const userMessage = messages[messages.length - 1].content.toLowerCase();

        // Generate appropriate Somali responses based on user input
        let response = '';

        if (userMessage.includes('salam') || userMessage.includes('hello') || userMessage.includes('hi')) {
            response = 'Salam alaykum! Waan ku soo dhaweynayaa Tincada AI. Sidee kuu caawin karaa maanta?';
        } else if (userMessage.includes('sidee tahay') || userMessage.includes('how are you')) {
            response = 'Alhamdulillah, waan fiicnahay! Waxaan ahay Tincada AI, kaaliyahaaga AI ah. Maxaan kuu qaban karaa?';
        } else if (userMessage.includes('maxaad tahay') || userMessage.includes('what are you')) {
            response = 'Waxaan ahay Tincada AI, chatbot casri ah oo af Soomaali ku hadla. Waxaan kaa caawin karaa su\'aalo badan, qorista, tarjumaad, iyo waxyaabo kale badan.';
        } else if (userMessage.includes('caawimaad') || userMessage.includes('help')) {
            response = 'Dabcan! Waxaan kaa caawin karaa:\n\n• Su\'aalo guud\n• Qorista iyo tafsiirka\n• Tarjumaad\n• Xisaab iyo cilmi\n• Talo iyo hagitaan\n\nMaxaad rabto inaad ka weydiiso?';
        } else if (userMessage.includes('mahadsanid') || userMessage.includes('thank')) {
            response = 'Adaa mudan! Waxaan ku faraxsanahay inaan ku caawinay. Haddii aad wax kale u baahan tahay, ha ka yaabina inaad i weydiiso!';
        } else if (userMessage.includes('waa maxay') || userMessage.includes('what is')) {
            response = 'Waa su\'aal fiican! Waxaan jeclaan lahaa inaan kuu sharaxo, laakiin waxaan u baahanahay macluumaad dheeraad ah si aan ugu jawaabo si sax ah. Fadlan ii sheeg waxaad rabto inaad ka ogaato.';
        } else if (userMessage.includes('gabay') || userMessage.includes('poem')) {
            response = 'Gabayga waa dhaxal qiimo leh oo Soomaaliyeed! Halkan waxaa ah gabay gaaban:\n\n"Guulaha raadinta, gacanta ku hay,\nGeesinimo iyo dadaal, gargaar ma leh,\nGacankaaga ku tiirsanow, guul baad heli."';
        } else if (userMessage.includes('canjeero') || userMessage.includes('food')) {
            response = 'Canjeero waa cunto caadi ah oo Soomaaliyeed! Waxay ka kooban tahay:\n\n• Bur (flour)\n• Biyo\n• Khamiir (yeast)\n• Sonkor yar\n\nWaxaa lagu kariyaa alwaax kulul, waxaana la cunaa subaxda shaah iyo hilib ama digaag.';
        } else {
            // Generic helpful response
            const responses = [
                'Waa su\'aal xiiso leh! Waxaan jeclaan lahaa inaan kuu caawiyo. Fadlan ii sheeg wax dheeraad ah si aan ugu jawaabo si fiican.',
                'Mahadsanid su\'aashaada! Waxaan ahay Tincada AI oo waxaan ku caawin karaa waxyaabo badan. Maxaad gaar ahaan rabto inaad ogaato?',
                'Fiican! Waxaan halkan u joogaa si aan kuu caawiyo. Haddii aad rabto jawaab gaar ah, fadlan ii sheeg faahfaahin dheeraad ah.',
                'Waa macquul! Waxaan ku caawin karaa su\'aalkaaga. Fadlan ii sheeg wax dheeraad ah si aan ugu jawaabo si buuxda.',
                'Aad ayaad u mahadsantahay inaad i weydiisay! Waxaan jeclaan lahaa inaan kuu bixiyo jawaab fiican. Maxaad gaar ahaan rabto inaad ka ogaato?'
            ];
            response = responses[Math.floor(Math.random() * responses.length)];
        }

        // Simulate API delay
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    success: true,
                    data: {
                        choices: [{
                            message: {
                                content: response
                            }
                        }]
                    }
                });
            }, 1000 + Math.random() * 2000); // 1-3 second delay
        });
    }

    addMessage(type, text) {
        const chatMessages = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        
        if (type === 'user' && this.currentUser && this.currentUser.profileImage) {
            avatar.innerHTML = `<img src="${this.currentUser.profileImage}" alt="${this.currentUser.fullName}">`;
        } else if (type === 'user') {
            avatar.innerHTML = '<i class="fas fa-user"></i>';
        } else {
            avatar.innerHTML = '<i class="fas fa-robot"></i>';
        }
        
        const content = document.createElement('div');
        content.className = 'message-content';
        content.innerHTML = `
            <div class="message-text">${this.formatMessage(text)}</div>
            <div class="message-time">${this.getCurrentTime()}</div>
        `;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        chatMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Update chat history
        this.chatHistory.push({
            type,
            text,
            timestamp: Date.now()
        });

        // Update message counts
        this.messageCount++;
        this.currentChatMessageCount++;

        // Only count user messages towards daily limit
        if (type === 'user') {
            this.dailyMessageCount++;
            this.saveDailyMessageCount();
        }

        this.saveChatData();
    }

    formatMessage(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    getCurrentTime() {
        const now = new Date();
        return now.toLocaleTimeString('so-SO', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    generateChatId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    addChatToList(chatId, title) {
        const chatList = document.getElementById('chatList');
        const chatItem = document.createElement('div');
        chatItem.className = 'chat-item active';
        chatItem.dataset.chatId = chatId;
        chatItem.innerHTML = `
            <i class="fas fa-comment"></i>
            <span class="chat-item-text">${title}</span>
            <div class="chat-item-menu">
                <i class="fas fa-ellipsis-h"></i>
            </div>
        `;
        
        // Remove active class from other chats
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
        });
        
        chatList.insertBefore(chatItem, chatList.firstChild);
        
        // Add click event
        chatItem.addEventListener('click', () => {
            this.loadChat(chatId);
        });
    }

    updateChatTitle(chatId, title) {
        const chatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
        if (chatItem) {
            const textElement = chatItem.querySelector('.chat-item-text');
            textElement.textContent = title;
        }
    }

    setupAutoResize() {
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.addEventListener('input', () => {
                this.autoResizeTextarea(textarea);
            });
        });
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    toggleVoiceRecording() {
        // Voice recording implementation
        this.showToast('Voice recording feature waa la dhisi doonaa', 'info');
    }

    searchChats(query) {
        const chatItems = document.querySelectorAll('.chat-item');
        chatItems.forEach(item => {
            const text = item.querySelector('.chat-item-text').textContent.toLowerCase();
            if (text.includes(query.toLowerCase())) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    switchNavigation(navId) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to clicked item
        document.getElementById(navId).classList.add('active');
        
        // Handle navigation logic
        switch(navId) {
            case 'chatsNav':
                // Show chats
                break;
            case 'libraryNav':
                this.showToast('Maktabada feature waa la dhisi doonaa', 'info');
                break;
            case 'soraNav':
                this.showToast('Sora feature waa la dhisi doonaa', 'info');
                break;
            case 'gptsNav':
                this.showToast('GPTs feature waa la dhisi doonaa', 'info');
                break;
        }
    }

    openSettings() {
        this.closeUserDropdown();
        document.getElementById('settingsModal').style.display = 'flex';
        this.loadSettingsContent();
    }

    closeSettings() {
        document.getElementById('settingsModal').style.display = 'none';
    }

    loadSettingsContent() {
        const modalBody = document.querySelector('.modal-body');
        modalBody.innerHTML = `
            <div class="settings-content">
                <h3>Profile Settings</h3>
                <div class="setting-group">
                    <label>Magaca Buuxa</label>
                    <input type="text" id="settingsName" value="${this.currentUser.fullName}" class="setting-input">
                </div>
                <div class="setting-group">
                    <label>Lambarka Telefoonka</label>
                    <input type="text" id="settingsPhone" value="${this.currentUser.phoneNumber}" class="setting-input">
                </div>
                <div class="setting-group">
                    <label>Sawirka Profile</label>
                    <div class="profile-upload-setting">
                        <div class="current-avatar">
                            ${this.currentUser.profileImage ?
                                `<img src="${this.currentUser.profileImage}" alt="Profile">` :
                                '<i class="fas fa-user"></i>'
                            }
                        </div>
                        <button class="change-avatar-btn" id="changeAvatarBtn">Beddel Sawirka</button>
                    </div>
                </div>

                <h3 style="margin-top: 30px;">API Settings</h3>
                <div class="setting-group">
                    <label>OpenAI API Key</label>
                    <input type="password" id="settingsApiKey" value="${this.apiKey}" class="setting-input" placeholder="sk-...">
                    <small style="color: #8e8ea0; font-size: 0.8rem; margin-top: 5px; display: block;">
                        Geli OpenAI API key cusub haddii kan hadda jira uusan shaqaynin
                    </small>
                </div>
                <div class="setting-group">
                    <label style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" id="useMockMode" ${this.useMockAPI ? 'checked' : ''}>
                        <span>Isticmaal Mock API (demo mode)</span>
                    </label>
                    <small style="color: #8e8ea0; font-size: 0.8rem; margin-top: 5px; display: block;">
                        Haddii API key-gu ma shaqaynin, mock responses ayaa la isticmaali doonaa
                    </small>
                </div>

                <div class="setting-actions">
                    <button class="save-settings-btn" id="saveSettingsBtn">Kaydi Isbeddelka</button>
                    <button class="cancel-settings-btn" id="cancelSettingsBtn">Ka Noqo</button>
                    <button class="test-api-btn" id="testApiBtn">Test API Key</button>
                </div>
            </div>
        `;
        
        // Add CSS for settings
        this.addSettingsCSS();
        
        // Bind settings events
        this.bindSettingsEvents();
    }

    addSettingsCSS() {
        if (!document.getElementById('settingsCSS')) {
            const style = document.createElement('style');
            style.id = 'settingsCSS';
            style.textContent = `
                .settings-content h3 {
                    color: #ffffff;
                    margin-bottom: 24px;
                    font-size: 1.2rem;
                }
                .setting-group {
                    margin-bottom: 20px;
                }
                .setting-group label {
                    display: block;
                    color: #ffffff;
                    margin-bottom: 8px;
                    font-weight: 500;
                }
                .setting-input {
                    width: 100%;
                    background: #171717;
                    border: 1px solid #3f3f3f;
                    color: #ffffff;
                    padding: 12px;
                    border-radius: 6px;
                    font-family: inherit;
                }
                .setting-input:focus {
                    outline: none;
                    border-color: #10a37f;
                }
                .profile-upload-setting {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                }
                .current-avatar {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: #10a37f;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;
                }
                .current-avatar img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                .current-avatar i {
                    color: #ffffff;
                    font-size: 1.5rem;
                }
                .change-avatar-btn {
                    background: #2f2f2f;
                    border: 1px solid #3f3f3f;
                    color: #ffffff;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .change-avatar-btn:hover {
                    background: #3f3f3f;
                }
                .setting-actions {
                    display: flex;
                    gap: 12px;
                    margin-top: 32px;
                }
                .save-settings-btn {
                    background: #10a37f;
                    border: none;
                    color: #ffffff;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.2s ease;
                }
                .save-settings-btn:hover {
                    background: #0d8f6f;
                }
                .cancel-settings-btn {
                    background: #2f2f2f;
                    border: 1px solid #3f3f3f;
                    color: #ffffff;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .cancel-settings-btn:hover {
                    background: #3f3f3f;
                }
                .test-api-btn {
                    background: #2563eb;
                    border: none;
                    color: #ffffff;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.2s ease;
                }
                .test-api-btn:hover {
                    background: #1d4ed8;
                }
                input[type="checkbox"] {
                    width: auto;
                    margin-right: 8px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    bindSettingsEvents() {
        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('cancelSettingsBtn').addEventListener('click', () => {
            this.closeSettings();
        });

        document.getElementById('changeAvatarBtn').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });

        document.getElementById('testApiBtn').addEventListener('click', () => {
            this.testApiKey();
        });

        document.getElementById('useMockMode').addEventListener('change', (e) => {
            this.useMockAPI = e.target.checked;
            if (this.useMockAPI) {
                this.showToast('Mock API mode waa la daahfuray', 'info');
            } else {
                this.showToast('Real API mode waa la daahfuray', 'info');
            }
        });
    }

    async testApiKey() {
        const apiKeyInput = document.getElementById('settingsApiKey');
        const testBtn = document.getElementById('testApiBtn');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            this.showToast('Fadlan geli API key', 'error');
            return;
        }

        testBtn.disabled = true;
        testBtn.textContent = 'Testing...';

        try {
            const testMessages = [
                {
                    role: 'system',
                    content: 'You are a helpful assistant. Respond in Somali.'
                },
                {
                    role: 'user',
                    content: 'Salam, test message'
                }
            ];

            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4o-mini',
                    messages: testMessages,
                    max_tokens: 50,
                    temperature: 0.7
                })
            });

            if (response.ok) {
                this.showToast('✅ API key waa shaqaynayaa!', 'success');
                this.apiKey = apiKey;
                this.useMockAPI = false;
                document.getElementById('useMockMode').checked = false;
            } else {
                const errorText = await response.text();
                this.showToast(`❌ API key ma shaqaynayo: ${response.status}`, 'error');
                console.error('API test failed:', errorText);
            }

        } catch (error) {
            this.showToast('❌ Network khalad: ' + error.message, 'error');
            console.error('API test error:', error);
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = 'Test API Key';
        }
    }

    saveSettings() {
        const newName = document.getElementById('settingsName').value.trim();
        const newPhone = document.getElementById('settingsPhone').value.trim();
        const newApiKey = document.getElementById('settingsApiKey').value.trim();
        const useMock = document.getElementById('useMockMode').checked;

        if (!newName || !newPhone) {
            this.showToast('Fadlan buuxi dhammaan goobaha', 'error');
            return;
        }

        // Update user data
        this.currentUser.fullName = newName;
        this.currentUser.phoneNumber = newPhone;

        // Update API settings
        if (newApiKey) {
            this.apiKey = newApiKey;
        }
        this.useMockAPI = useMock;

        // Save to localStorage
        authManager.login(this.currentUser);
        localStorage.setItem('tincadaAI_apiKey', this.apiKey);
        localStorage.setItem('tincadaAI_useMockAPI', this.useMockAPI.toString());

        // Update UI
        this.loadUserProfile();

        this.showToast('Settings waa la kaydiyay!', 'success');
        this.updateAPIStatusBanner();
        this.closeSettings();
    }

    updateAPIStatusBanner() {
        const banner = document.getElementById('apiStatusBanner');
        const bannerDismissed = localStorage.getItem('tincadaAI_bannerDismissed');

        if (this.useMockAPI && !bannerDismissed) {
            banner.style.display = 'block';
        } else {
            banner.style.display = 'none';
        }
    }

    hideAPIStatusBanner() {
        const banner = document.getElementById('apiStatusBanner');
        banner.style.display = 'none';

        // Temporarily hide banner (user dismissed it)
        localStorage.setItem('tincadaAI_bannerDismissed', 'true');

        // Show it again after 1 hour if still using mock API
        setTimeout(() => {
            localStorage.removeItem('tincadaAI_bannerDismissed');
            this.updateAPIStatusBanner();
        }, 60 * 60 * 1000); // 1 hour
    }

    showPaymentModal() {
        document.getElementById('paymentModal').style.display = 'flex';

        // Update the modal with current usage
        const limitReached = document.querySelector('.limit-reached p');
        limitReached.innerHTML = `Waxaad diray <strong>${this.dailyMessageCount} fariin</strong> maanta. Si aad u sii waddo, fadlan doorso mid ka mid ah plans-yada hoose:`;

        this.showToast('Xadka fariimaha maalinlaha ah waa la gaaray!', 'warning');
    }

    showUpgradeModal() {
        // Show payment modal but with upgrade messaging
        document.getElementById('paymentModal').style.display = 'flex';

        // Update the modal with upgrade messaging
        const limitReached = document.querySelector('.limit-reached');
        limitReached.innerHTML = `
            <i class="fas fa-crown"></i>
            <h3>Upgrade to Tincada AI Plus!</h3>
            <p>Hel unlimited messages, advanced features, iyo priority support. Doorso plan-ka kugu habboon:</p>
        `;

        this.showToast('Upgrade to Plus for unlimited access!', 'info');
    }

    closePaymentModal() {
        document.getElementById('paymentModal').style.display = 'none';
    }

    selectPlan(planType) {
        console.log('🛒 Plan selected:', planType);

        // Show payment form
        this.showPaymentForm(planType);
    }

    processPlanUpgrade(planType) {
        let newLimit = 1000;
        let planName = '';

        switch(planType) {
            case 'premium':
                newLimit = 999999; // Unlimited
                planName = 'Premium Plan';
                break;
            case 'basic':
                newLimit = 5000;
                planName = 'Basic Plan';
                break;
            case 'daily':
                newLimit = this.dailyMessageCount + 2000;
                planName = 'Daily Pass';
                break;
        }

        // Update limits
        this.maxMessages = newLimit;

        // Save upgrade info
        const upgradeInfo = {
            plan: planType,
            planName: planName,
            upgradeDate: new Date().toISOString(),
            newLimit: newLimit
        };

        localStorage.setItem('tincadaAI_planUpgrade', JSON.stringify(upgradeInfo));

        // Close modal and show success
        this.closePaymentModal();
        this.showToast(`✅ ${planName} waa la activate gareeyay! Hadda waxaad diri kartaa ${newLimit === 999999 ? 'unlimited' : newLimit} fariin.`, 'success');

        // Update UI
        this.updateMessageCountDisplay();

        console.log('💳 Plan upgraded:', upgradeInfo);
    }

    showPaymentForm(planType) {
        // Hide payment modal and show payment form
        document.getElementById('paymentModal').style.display = 'none';
        document.getElementById('paymentFormModal').style.display = 'flex';

        // Update plan summary
        this.updatePlanSummary(planType);

        // Setup form validation
        this.setupFormValidation();

        this.showToast('Buuxi payment form si aad u dhamaystirto purchase-ka', 'info');
    }

    closePaymentFormModal() {
        document.getElementById('paymentFormModal').style.display = 'none';
    }

    backToPlans() {
        // Hide payment form and show payment modal
        document.getElementById('paymentFormModal').style.display = 'none';
        document.getElementById('paymentModal').style.display = 'flex';
    }

    updatePlanSummary(planType) {
        const planData = {
            premium: {
                name: 'Premium Plan',
                price: '$9.99/month',
                benefits: [
                    '✅ Unlimited messages',
                    '✅ Priority support',
                    '✅ Advanced AI features',
                    '✅ File upload (50MB)',
                    '✅ Voice messages'
                ]
            },
            basic: {
                name: 'Basic Plan',
                price: '$4.99/month',
                benefits: [
                    '✅ 5,000 messages/day',
                    '✅ Standard support',
                    '✅ Basic AI features',
                    '✅ File upload (10MB)'
                ]
            },
            daily: {
                name: 'Daily Pass',
                price: '$1.99/day',
                benefits: [
                    '✅ 2,000 messages today',
                    '✅ All AI features',
                    '✅ File upload (25MB)',
                    '✅ Voice messages'
                ]
            }
        };

        const plan = planData[planType];
        document.getElementById('selectedPlanName').textContent = plan.name;
        document.getElementById('selectedPlanPrice').textContent = plan.price;

        const benefitsList = document.getElementById('selectedPlanBenefits');
        benefitsList.innerHTML = plan.benefits.map(benefit => `<li>${benefit}</li>`).join('');

        // Store selected plan for processing
        this.selectedPlan = planType;
    }

    setupFormValidation() {
        // Card number formatting
        const cardNumberInput = document.getElementById('cardNumber');
        cardNumberInput.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });

        // Expiry date formatting
        const expiryInput = document.getElementById('expiryDate');
        expiryInput.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });

        // CVV validation
        const cvvInput = document.getElementById('cvv');
        cvvInput.addEventListener('input', (e) => {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });
    }

    processPayment() {
        const form = document.getElementById('paymentForm');
        const formData = new FormData(form);

        // Validate form
        if (!form.checkValidity()) {
            this.showToast('Fadlan buuxi dhammaan goobaha loo baahan yahay', 'error');
            return;
        }

        // Show processing state
        const submitBtn = document.getElementById('completePaymentBtn');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        submitBtn.disabled = true;

        // Simulate payment processing
        setTimeout(() => {
            this.completePayment();

            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    }

    completePayment() {
        // Process the upgrade
        this.processPlanUpgrade(this.selectedPlan);

        // Close payment form
        this.closePaymentFormModal();

        // Show success message
        this.showToast('🎉 Payment successful! Plan waa la activate gareeyay!', 'success');

        console.log('💳 Payment completed for plan:', this.selectedPlan);
    }

    loadChatHistory() {
        // Load saved chats from localStorage
        const savedChats = localStorage.getItem('tincadaAI_chats');
        if (savedChats) {
            this.chats = JSON.parse(savedChats);
            this.renderChatHistory();
        }
    }

    saveChatData() {
        localStorage.setItem('tincadaAI_chats', JSON.stringify(this.chats));
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    logout() {
        if (confirm('Ma hubtaa inaad rabto inaad ka baxdo?')) {
            authManager.logout();
            
            document.body.style.opacity = '0';
            document.body.style.transform = 'scale(0.9)';
            
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 500);
        }
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TincadaDashboard();
});
